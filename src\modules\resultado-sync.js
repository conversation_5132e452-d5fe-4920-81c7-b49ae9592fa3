/**
 * Resultado Sync System
 * Sincroniza dados entre patrimônio/alocação e a seção de resultados
 * Gerencia valores, percentuais, comissões e visibilidade de elementos
 */

import { ComissoesUtils } from '../config/comissoes-config.js';

// Utils object (same as in patrimony-sync.js)
const Utils = {
  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  },

  parseCurrencyValue(value) {
    if (!value || typeof value !== 'string') return 0;
    const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
    return parseFloat(cleanValue) || 0;
  },

  calculatePercentage(value, total) {
    if (!total || total === 0) return 0;
    return (value / total) * 100;
  },

  formatPercentage(value) {
    return `${value.toFixed(1)}%`;
  },

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
};

/* global requestAnimationFrame */

export class ResultadoSyncSystem {
  constructor() {
    this.isInitialized = false;
    this.patrimonioItems = new Map();
    this.resultadoPatrimonioItems = new Map();
    this.resultadoComissaoItems = new Map();
    this.selectedAssets = new Set();
    this.totalPatrimonio = 0;

    // Cache configuration
    this.cacheKey = 'resultado_sync_data';

    // Mapeamento para corrigir inconsistências nos atributos
    this.attributeMapping = {
      // Patrimônio -> Resultado mapping
      'Outros-Popupança': 'Outros-Poupança', // Corrige typo "Popupança"
      'Operação compromissada-Criptoativos': 'Outros-Criptoativos', // Corrige categoria errada
      'Fundo de Investimento-Liquidez': 'Fundo de Investimento-Liqudez', // Corrige typo no resultado
      'Fundo de Investimento-Ações': 'Fundo de investimento-Ações',
      'Fundo de Investimento-Renda Fixa': 'Fundo de investimento-Renda Fixa',
      'Renda Variável-Ações': 'Renda Variável-Ações', // Mantém capitalização do resultado
      'Renda Variável-Estruturada': 'Renda Variável-Estruturada',
      'Renda Variável-Carteira administrada': 'Renda Variável-Carteira administrada',
    };
  }

  // Cache Manager - similar to other modules
  get CacheManager() {
    return {
      set: (key, value) => {
        try {
          window.localStorage.setItem(key, JSON.stringify(value));
        } catch {
          // Silent fail
        }
      },
      get: (key) => {
        try {
          const value = window.localStorage.getItem(key);
          return value ? JSON.parse(value) : null;
        } catch {
          return null;
        }
      },
      remove: (key) => {
        try {
          window.localStorage.removeItem(key);
        } catch {
          // Silent fail
        }
      },
    };
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    this.waitForDOM().then(() => {
      // Wait for other systems to be ready
      this.waitForDependencies().then(() => {
        this.cacheElements();
        this.setupEventListeners();
        this.setupCacheListeners();
        this.loadCachedData();
        this.initialSync();
        this.isInitialized = true;

        // Dispatch ready event
        document.dispatchEvent(
          new CustomEvent('resultadoSyncReady', {
            detail: { system: this },
          })
        );
      });
    });
  }

  waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  // Normaliza chaves para corrigir inconsistências
  normalizeKey(category, product) {
    const originalKey = `${category}-${product}`;
    return this.attributeMapping[originalKey] || originalKey;
  }

  // Obtém chave mapeada para resultado
  getResultKey(patrimonioKey) {
    return this.attributeMapping[patrimonioKey] || patrimonioKey;
  }

  waitForDependencies() {
    return new Promise((resolve) => {
      const checkDependencies = () => {
        // Check if patrimony sync is ready
        const patrimonySyncReady = document.querySelector('.patrimonio_interactive_item');
        // Check if resultado section exists
        const resultadoSection = document.querySelector('._4-section-resultado');

        if (patrimonySyncReady && resultadoSection) {
          resolve();
        } else {
          setTimeout(checkDependencies, 100);
        }
      };
      checkDependencies();
    });
  }

  cacheElements() {
    // Cache main patrimonio total element using data attribute
    this.patrimonioTotalElement = document.querySelector('[data-patrimonio-total="ativo"]');

    // Cache patrimônio items (seção 3)
    const patrimonioElements = document.querySelectorAll('.patrimonio_interactive_item');

    patrimonioElements.forEach((element) => {
      const category = element.getAttribute('ativo-category');
      const product = element.getAttribute('ativo-product');

      if (category && product) {
        const key = `${category}-${product}`;
        const input = element.querySelector('.currency-input.individual');
        const slider = element.querySelector('range-slider');
        const percentage = element.querySelector('.porcentagem-calculadora');

        this.patrimonioItems.set(key, {
          element,
          category,
          product,
          input,
          slider,
          percentage,
          key,
        });
      }
    });

    // Cache resultado patrimônio items
    const resultadoPatrimonioElements = document.querySelectorAll(
      '.patrimonio-ativos-group .ativos-produtos-item'
    );

    resultadoPatrimonioElements.forEach((element) => {
      const category = element.getAttribute('ativo-category');
      const product = element.getAttribute('ativo-product');

      if (category && product) {
        const key = `${category}-${product}`;
        const valorElement = element.querySelector('.ativo-dinheiro .valor-minimo');
        const percentageElement = element.querySelector('.porcentagem-calculadora.v2');

        this.resultadoPatrimonioItems.set(key, {
          element,
          category,
          product,
          valorElement,
          percentageElement,
          key,
        });
      }
    });

    // Cache resultado comissão items
    const resultadoComissaoElements = document.querySelectorAll(
      '.ativos-content-float .ativos-produtos-item'
    );
    resultadoComissaoElements.forEach((element) => {
      const category = element.getAttribute('ativo-category');
      const product = element.getAttribute('ativo-product');

      if (category && product) {
        const key = `${category}-${product}`;
        const taxaMinimaElement = element.querySelector('.ativos-produto-porcentagem .taxa-minima');
        const taxaMaximaElement = element.querySelector('.ativos-produto-porcentagem .taxa-maxima');
        const valorMinimoElement = element.querySelector(
          '.ativo-valor-minimo .ativo-dinheiro .valor-minimo'
        );
        const valorMaximoElement = element.querySelector(
          '.ativo-valor-maximo .ativo-dinheiro .valor-maximo'
        );

        this.resultadoComissaoItems.set(key, {
          element,
          category,
          product,
          taxaMinimaElement,
          taxaMaximaElement,
          valorMinimoElement,
          valorMaximoElement,
          key,
        });
      }
    });
  }

  setupEventListeners() {
    // Listen for patrimony changes
    document.addEventListener('patrimonySyncReady', () => {
      this.syncAllValues();
    });

    // Listen for input changes from currency system
    document.addEventListener('currencyInputChanged', (event) => {
      this.handlePatrimonioChange(event.detail);
    });

    // Listen for slider changes
    document.addEventListener('sliderChanged', (event) => {
      this.handleSliderChange(event.detail);
    });

    // Listen for asset selection changes
    document.addEventListener('assetSelectionChanged', (event) => {
      this.handleAssetSelectionChange(event.detail);
    });

    // Listen for main patrimonio changes (evento correto do patrimony-sync)
    document.addEventListener('patrimonyMainValueChanged', (event) => {
      this.handleMainPatrimonioChanged(event.detail);
    });

    // Listen for allocation changes
    document.addEventListener('allocationChanged', () => {
      this.syncAllValues();
    });

    // Listen for allocation status changes
    document.addEventListener('allocationStatusChanged', () => {
      this.syncAllValues();
    });

    // Listen for patrimony sync updates
    document.addEventListener('patrimonyValueChanged', (event) => {
      this.handlePatrimonyValueChanged(event.detail);
    });

    // Setup direct input listeners for immediate sync
    this.setupDirectInputListeners();
  }

  setupDirectInputListeners() {
    // Listen to main patrimonio input
    const mainInput = document.querySelector('.currency-input.main');
    if (mainInput) {
      mainInput.addEventListener(
        'input',
        Utils.debounce(() => {
          this.syncTotalPatrimonio();
        }, 300)
      );

      mainInput.addEventListener('currencyChange', () => {
        this.syncTotalPatrimonio();
      });
    }
    this.patrimonioItems.forEach((item, key) => {
      if (item.input) {
        // Listen for input events
        item.input.addEventListener(
          'input',
          Utils.debounce(() => {
            this.syncPatrimonioItem(key);
            this.syncComissaoItem(key);
          }, 300)
        );

        // Listen for currency change events
        item.input.addEventListener('currencyChange', () => {
          this.syncPatrimonioItem(key);
          this.syncComissaoItem(key);
        });
      }

      if (item.slider) {
        // Listen for slider changes
        item.slider.addEventListener('input', () => {
          this.syncPatrimonioItem(key);
          this.syncComissaoItem(key);
        });
      }
    });
  }

  setupCacheListeners() {
    // Listen for patrimony sync reset to clear our cache
    document.addEventListener('patrimonySyncReset', () => {
      this.clearCache();
    });

    // Listen for asset selection system reset
    document.addEventListener('assetSelectionSystemReset', () => {
      this.clearCache();
    });
  }

  /**
   * Save current resultado data to cache
   */
  saveCachedData() {
    try {
      const cacheData = {
        totalPatrimonio: this.totalPatrimonio,
        selectedAssets: Array.from(this.selectedAssets),
        timestamp: Date.now(),
        // Save current display values for restoration
        displayValues: this.getCurrentDisplayValues(),
      };
      this.CacheManager.set(this.cacheKey, cacheData);
    } catch (error) {
      console.warn('Error saving resultado data to cache:', error);
    }
  }

  /**
   * Load resultado data from cache and restore state
   */
  loadCachedData() {
    try {
      const cachedData = this.CacheManager.get(this.cacheKey);
      if (!cachedData) {
        // If no cached data, ensure containers are hidden initially
        this.updateMainContainersVisibility(false);
        return;
      }

      // Restore basic data
      this.totalPatrimonio = cachedData.totalPatrimonio || 0;
      this.selectedAssets = new Set(cachedData.selectedAssets || []);

      // Restore display values if available
      if (cachedData.displayValues) {
        this.restoreDisplayValues(cachedData.displayValues);
      }

      // Update visibility based on cached selections
      this.updateVisibility();

      // console.log(`📦 Resultado data loaded from cache: ${this.selectedAssets.size} assets, total: R$ ${Utils.formatCurrency(this.totalPatrimonio)}`);
    } catch (error) {
      console.warn('Error loading resultado data from cache:', error);
      // On error, ensure containers are hidden
      this.updateMainContainersVisibility(false);
    }
  }

  /**
   * Clear resultado cache
   */
  clearCache() {
    try {
      this.CacheManager.remove(this.cacheKey);
    } catch (error) {
      console.warn('Error clearing resultado cache:', error);
    }
  }

  /**
   * Get current display values for caching
   */
  getCurrentDisplayValues() {
    const displayValues = {};

    // Cache patrimonio display values
    this.resultadoPatrimonioItems.forEach((item, key) => {
      if (item.valorElement && item.percentageElement) {
        displayValues[key] = {
          valor: item.valorElement.textContent,
          percentage: item.percentageElement.textContent,
        };
      }
    });

    // Cache comissao display values
    this.resultadoComissaoItems.forEach((item, key) => {
      if (
        item.taxaMinimaElement &&
        item.taxaMaximaElement &&
        item.valorMinimoElement &&
        item.valorMaximoElement
      ) {
        displayValues[`${key}_comissao`] = {
          taxaMinima: item.taxaMinimaElement.textContent,
          taxaMaxima: item.taxaMaximaElement.textContent,
          valorMinimo: item.valorMinimoElement.textContent,
          valorMaximo: item.valorMaximoElement.textContent,
        };
      }
    });

    return displayValues;
  }

  /**
   * Restore display values from cache
   */
  restoreDisplayValues(displayValues) {
    // Restore patrimonio display values
    this.resultadoPatrimonioItems.forEach((item, key) => {
      const cachedValue = displayValues[key];
      if (cachedValue && item.valorElement && item.percentageElement) {
        item.valorElement.textContent = cachedValue.valor;
        item.percentageElement.textContent = cachedValue.percentage;
      }
    });

    // Restore comissao display values
    this.resultadoComissaoItems.forEach((item, key) => {
      const cachedValue = displayValues[`${key}_comissao`];
      if (
        cachedValue &&
        item.taxaMinimaElement &&
        item.taxaMaximaElement &&
        item.valorMinimoElement &&
        item.valorMaximoElement
      ) {
        item.taxaMinimaElement.textContent = cachedValue.taxaMinima;
        item.taxaMaximaElement.textContent = cachedValue.taxaMaxima;
        item.valorMinimoElement.textContent = cachedValue.valorMinimo;
        item.valorMaximoElement.textContent = cachedValue.valorMaximo;
      }
    });
  }

  initialSync() {
    // Get total patrimonio from PatrimonySync system
    if (window.PatrimonySync && typeof window.PatrimonySync.getMainValue === 'function') {
      this.totalPatrimonio = window.PatrimonySync.getMainValue();
    } else {
      // Fallback: obtém diretamente do input principal
      const mainInput = document.querySelector('.currency-input.main');
      if (mainInput) {
        this.totalPatrimonio = Utils.parseCurrencyValue(mainInput.value);
      }
    }

    this.syncAllValues();
    this.updateVisibility();
  }

  syncAllValues() {
    // Sync total patrimonio first
    this.syncTotalPatrimonio();

    this.patrimonioItems.forEach((_, key) => {
      this.syncPatrimonioItem(key);
      this.syncComissaoItem(key);
    });
  }

  syncTotalPatrimonio() {
    // Usa exatamente o mesmo código do patrimony-sync, mas com o atributo
    const totalEl = document.querySelector('[data-patrimonio-total="true"]');
    if (totalEl && window.PatrimonySync) {
      const mainValue = window.PatrimonySync.getMainValue();
      totalEl.textContent = Utils.formatCurrency(mainValue);
    }
  }

  syncPatrimonioItem(key) {
    const patrimonioItem = this.patrimonioItems.get(key);
    const resultKey = this.getResultKey(key);
    const resultadoItem = this.resultadoPatrimonioItems.get(resultKey);

    if (!patrimonioItem || !resultadoItem) {
      return;
    }

    // Get current values
    const inputValue = patrimonioItem.input
      ? Utils.parseCurrencyValue(patrimonioItem.input.value)
      : 0;

    const sliderValue = patrimonioItem.slider ? parseFloat(patrimonioItem.slider.value) : 0;

    const percentage = sliderValue * 100;

    // Update resultado patrimônio values
    if (resultadoItem.valorElement) {
      resultadoItem.valorElement.textContent = Utils.formatCurrency(inputValue);
    }

    if (resultadoItem.percentageElement) {
      resultadoItem.percentageElement.textContent = `${percentage.toFixed(1)}%`;
    }
  }

  syncComissaoItem(key) {
    const patrimonioItem = this.patrimonioItems.get(key);
    const resultKey = this.getResultKey(key);
    const comissaoItem = this.resultadoComissaoItems.get(resultKey);

    if (!patrimonioItem || !comissaoItem) {
      return;
    }

    const inputValue = patrimonioItem.input
      ? Utils.parseCurrencyValue(patrimonioItem.input.value)
      : 0;

    // Get commission rates
    const comissaoData = this.getComissaoData(patrimonioItem.category, patrimonioItem.product);

    if (comissaoData) {
      // Update percentage rates
      if (comissaoItem.taxaMinimaElement) {
        comissaoItem.taxaMinimaElement.textContent = `${comissaoData.min}%`;
      }

      if (comissaoItem.taxaMaximaElement) {
        comissaoItem.taxaMaximaElement.textContent = `${comissaoData.max}%`;
      }

      // Calculate commission values
      const valorMinimo = (inputValue * comissaoData.min) / 100;
      const valorMaximo = (inputValue * comissaoData.max) / 100;

      // Update commission values
      if (comissaoItem.valorMinimoElement) {
        comissaoItem.valorMinimoElement.textContent = Utils.formatCurrency(valorMinimo);
      }

      if (comissaoItem.valorMaximoElement) {
        comissaoItem.valorMaximoElement.textContent = Utils.formatCurrency(valorMaximo);
      }
    }
  }

  getComissaoData(category, product) {
    return ComissoesUtils.getComissaoData(category, product);
  }

  handlePatrimonioChange(detail) {
    if (detail.key) {
      this.syncPatrimonioItem(detail.key);
      this.syncComissaoItem(detail.key);
      // Save to cache after sync
      this.saveCachedData();
    }
  }

  handleSliderChange(detail) {
    if (detail.key) {
      this.syncPatrimonioItem(detail.key);
      this.syncComissaoItem(detail.key);
      // Save to cache after sync
      this.saveCachedData();
    }
  }

  handleAssetSelectionChange(detail) {
    // Convert normalized keys from asset selection to our key format
    const selectedAssets = (detail.selectedAssets || []).map((normalizedKey) => {
      // Convert from "category|product" (normalized) to "Category-Product" (our format)
      return this.convertNormalizedKeyToResultKey(normalizedKey);
    });

    this.selectedAssets = new Set(selectedAssets);
    this.updateVisibility();
    // Save to cache after selection change
    this.saveCachedData();
  }

  /**
   * Convert normalized key from asset selection to resultado key format
   * From: "renda fixa|cdb" (normalized, pipe separator)
   * To: "Renda Fixa-CDB" (proper case, dash separator)
   */
  convertNormalizedKeyToResultKey(normalizedKey) {
    const [category, product] = normalizedKey.split('|');

    // Find matching key in our maps by comparing normalized versions
    for (const [resultKey] of this.resultadoPatrimonioItems) {
      const [resultCategory, resultProduct] = resultKey.split('-');
      if (
        this.normalizeString(resultCategory) === category &&
        this.normalizeString(resultProduct) === product
      ) {
        return resultKey;
      }
    }

    // If not found in patrimonio items, check comissao items
    for (const [resultKey] of this.resultadoComissaoItems) {
      const [resultCategory, resultProduct] = resultKey.split('-');
      if (
        this.normalizeString(resultCategory) === category &&
        this.normalizeString(resultProduct) === product
      ) {
        return resultKey;
      }
    }

    // Fallback: convert to proper case format
    return `${this.toProperCase(category)}-${this.toProperCase(product)}`;
  }

  /**
   * Normalize string for comparison (same as asset selection filter)
   */
  normalizeString(str) {
    return str.toLowerCase().trim();
  }

  /**
   * Convert string to proper case
   */
  toProperCase(str) {
    return str
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  handlePatrimonyValueChanged(detail) {
    // Handle updates from the patrimony sync system
    if (detail.key) {
      this.syncPatrimonioItem(detail.key);
      this.syncComissaoItem(detail.key);
    } else {
      // If no specific key, sync all
      this.syncAllValues();
    }
    // Save to cache after sync
    this.saveCachedData();
  }

  handleMainPatrimonioChanged(detail) {
    // Handle main patrimonio value changes
    this.totalPatrimonio = detail.value;
    this.syncTotalPatrimonio();
    this.syncAllValues();
    // Save to cache after main value change
    this.saveCachedData();
  }

  updateVisibility() {
    // Check if any assets are selected
    const hasSelectedAssets = this.selectedAssets.size > 0;

    // Update main containers visibility
    this.updateMainContainersVisibility(hasSelectedAssets);

    if (!hasSelectedAssets) {
      // If no assets selected, hide everything and return
      this.hideAllItems();
      return;
    }

    // Update individual patrimônio items visibility with animation
    this.resultadoPatrimonioItems.forEach((item, key) => {
      const hasValue = this.hasValue(key);
      const isSelected = this.isAssetSelectedForResult(key);

      // Show if has value OR if asset is selected
      const shouldShow = hasValue || isSelected;

      this.animateVisibility(item.element, shouldShow);
    });

    // Update individual comissão items visibility with animation
    this.resultadoComissaoItems.forEach((item, key) => {
      const hasValue = this.hasValue(key);
      const isSelected = this.isAssetSelectedForResult(key);

      // Show if has value OR if asset is selected
      const shouldShow = hasValue || isSelected;

      this.animateVisibility(item.element, shouldShow);
    });

    // Update group headers visibility
    this.updateGroupHeadersVisibility();
  }

  /**
   * Update main containers visibility based on asset selection
   */
  updateMainContainersVisibility(hasSelectedAssets) {
    // Find main containers
    const patrimonioAtivosGroup = document.querySelector('.patrimonio-ativos-group');
    const ativosContentFloat = document.querySelector('.ativos-content-float');

    if (patrimonioAtivosGroup) {
      if (hasSelectedAssets) {
        patrimonioAtivosGroup.style.display = '';
        patrimonioAtivosGroup.style.opacity = '1';
      } else {
        patrimonioAtivosGroup.style.display = 'none';
        patrimonioAtivosGroup.style.opacity = '0';
      }
    }

    if (ativosContentFloat) {
      if (hasSelectedAssets) {
        ativosContentFloat.style.display = '';
        ativosContentFloat.style.opacity = '1';
      } else {
        ativosContentFloat.style.display = 'none';
        ativosContentFloat.style.opacity = '0';
      }
    }
  }

  /**
   * Hide all individual items when no assets are selected
   */
  hideAllItems() {
    // Hide all patrimônio items
    this.resultadoPatrimonioItems.forEach((item) => {
      this.animateVisibility(item.element, false);
    });

    // Hide all comissão items
    this.resultadoComissaoItems.forEach((item) => {
      this.animateVisibility(item.element, false);
    });
  }

  /**
   * Check if an asset is selected for result display
   * Handles the key format conversion and mapping
   */
  isAssetSelectedForResult(resultKey) {
    // If no assets are selected, don't show any individual items
    // (the main containers will be hidden)
    if (this.selectedAssets.size === 0) {
      return false;
    }

    // Direct check first
    if (this.selectedAssets.has(resultKey)) {
      return true;
    }

    // Check with attribute mapping
    const mappedKey = this.getResultKey(resultKey);
    if (this.selectedAssets.has(mappedKey)) {
      return true;
    }

    return false;
  }

  hasValue(key) {
    const patrimonioItem = this.patrimonioItems.get(key);
    if (!patrimonioItem || !patrimonioItem.input) return false;

    const value = Utils.parseCurrencyValue(patrimonioItem.input.value);
    return value > 0;
  }

  animateVisibility(element, shouldShow) {
    if (!element) return;

    if (shouldShow) {
      // Show element
      if (element.style.display === 'none') {
        element.style.display = 'block';
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';

        // Animate in
        const animateIn = () => {
          element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
          element.style.opacity = '1';
          element.style.transform = 'translateY(0)';
        };

        if (typeof requestAnimationFrame !== 'undefined') {
          requestAnimationFrame(animateIn);
        } else {
          setTimeout(animateIn, 16);
        }
      }
    } else {
      // Hide element
      if (element.style.display !== 'none') {
        element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';

        setTimeout(() => {
          if (element.style.opacity === '0') {
            element.style.display = 'none';
          }
        }, 300);
      }
    }
  }

  updateGroupHeadersVisibility() {
    // Get all group containers
    const patrimonioGroups = document.querySelectorAll(
      '.patrimonio-ativos-group .ativos-group-produtos'
    );
    const comissaoGroups = document.querySelectorAll('.ativos-content-float .ativos-group');

    // Update patrimônio groups
    patrimonioGroups.forEach((group) => {
      const visibleItems = group.querySelectorAll(
        '.ativos-produtos-item:not([style*="display: none"])'
      );
      const shouldShow = visibleItems.length > 0;
      this.animateVisibility(group, shouldShow);
    });

    // Update comissão groups
    comissaoGroups.forEach((group) => {
      const visibleItems = group.querySelectorAll(
        '.ativos-produtos-item:not([style*="display: none"])'
      );
      const shouldShow = visibleItems.length > 0;
      this.animateVisibility(group, shouldShow);
    });
  }

  // Public API methods
  updateTotalPatrimonio(value) {
    this.totalPatrimonio = value;
    this.syncAllValues();
  }

  getResultadoData() {
    const data = {
      patrimonio: {},
      comissoes: {},
      total: this.totalPatrimonio,
    };

    this.patrimonioItems.forEach((item, key) => {
      const inputValue = item.input ? Utils.parseCurrencyValue(item.input.value) : 0;
      const sliderValue = item.slider ? parseFloat(item.slider.value) : 0;

      data.patrimonio[key] = {
        valor: inputValue,
        percentual: sliderValue * 100,
        category: item.category,
        product: item.product,
      };

      const comissaoData = this.getComissaoData(item.category, item.product);
      if (comissaoData) {
        data.comissoes[key] = {
          taxaMin: comissaoData.min,
          taxaMax: comissaoData.max,
          valorMin: (inputValue * comissaoData.min) / 100,
          valorMax: (inputValue * comissaoData.max) / 100,
        };
      }
    });

    return data;
  }

  forceSync() {
    this.syncAllValues();
    this.updateVisibility();
  }

  // Debug methods
  debugInfo() {
    const patrimonioKeys = Array.from(this.patrimonioItems.keys());
    const resultadoPatrimonioKeys = Array.from(this.resultadoPatrimonioItems.keys());
    const resultadoComissaoKeys = Array.from(this.resultadoComissaoItems.keys());

    // Check mappings
    const mappingStatus = {};
    patrimonioKeys.forEach((key) => {
      const resultKey = this.getResultKey(key);
      const hasPatrimonioMatch = this.resultadoPatrimonioItems.has(resultKey);
      const hasComissaoMatch = this.resultadoComissaoItems.has(resultKey);

      mappingStatus[key] = {
        resultKey,
        hasPatrimonioMatch,
        hasComissaoMatch,
        isMapped: key !== resultKey,
      };
    });

    return {
      isInitialized: this.isInitialized,
      patrimonioItems: this.patrimonioItems.size,
      resultadoPatrimonioItems: this.resultadoPatrimonioItems.size,
      resultadoComissaoItems: this.resultadoComissaoItems.size,
      selectedAssets: this.selectedAssets.size,
      totalPatrimonio: this.totalPatrimonio,
      patrimonioKeys,
      resultadoPatrimonioKeys,
      resultadoComissaoKeys,
      mappingStatus,
      attributeMapping: this.attributeMapping,
    };
  }

  testSync() {
    // Test total patrimonio sync
    this.syncTotalPatrimonio();

    // Test with first patrimonio item
    const firstKey = this.patrimonioItems.keys().next().value;
    if (firstKey) {
      this.syncPatrimonioItem(firstKey);
      this.syncComissaoItem(firstKey);
    }

    // Test visibility
    this.updateVisibility();
  }

  /**
   * Reset the entire resultado system and clear cache
   */
  resetSystem() {
    try {
      // Clear cache
      this.clearCache();

      // Reset internal state
      this.selectedAssets.clear();
      this.totalPatrimonio = 0;

      // Reset all display values to zero
      this.resetAllDisplayValues();

      // Update visibility
      this.updateVisibility();

      // Dispatch reset event
      document.dispatchEvent(
        new CustomEvent('resultadoSyncReset', {
          detail: {
            timestamp: Date.now(),
          },
        })
      );
    } catch (error) {
      console.warn('Error resetting resultado system:', error);
    }
  }

  /**
   * Reset all display values to zero
   */
  resetAllDisplayValues() {
    // Reset patrimonio display values
    this.resultadoPatrimonioItems.forEach((item) => {
      if (item.valorElement) {
        item.valorElement.textContent = '0,00';
      }
      if (item.percentageElement) {
        item.percentageElement.textContent = '0%';
      }
    });

    // Reset comissao display values
    this.resultadoComissaoItems.forEach((item) => {
      if (item.taxaMinimaElement) {
        item.taxaMinimaElement.textContent = '0%';
      }
      if (item.taxaMaximaElement) {
        item.taxaMaximaElement.textContent = '0%';
      }
      if (item.valorMinimoElement) {
        item.valorMinimoElement.textContent = '0,00';
      }
      if (item.valorMaximoElement) {
        item.valorMaximoElement.textContent = '0,00';
      }
    });

    // Hide main containers when resetting
    this.updateMainContainersVisibility(false);
  }

  /**
   * Get cache information for debugging
   */
  getCacheInfo() {
    try {
      const cachedData = this.CacheManager.get(this.cacheKey);
      return {
        hasCachedData: !!cachedData,
        totalPatrimonio: cachedData?.totalPatrimonio || 0,
        selectedAssetsCount: cachedData?.selectedAssets?.length || 0,
        timestamp: cachedData?.timestamp || null,
        currentTotalPatrimonio: this.totalPatrimonio,
        currentSelectedAssets: this.selectedAssets.size,
        displayValuesCount: cachedData?.displayValues
          ? Object.keys(cachedData.displayValues).length
          : 0,
      };
    } catch (error) {
      console.warn('Error getting cache info:', error);
      return null;
    }
  }

  /**
   * Debug method to check key mappings and visibility
   */
  debugKeyMappings() {
    console.warn('=== RESULTADO SYNC DEBUG ===');
    console.warn('Selected Assets:', Array.from(this.selectedAssets));
    console.warn('Has Selected Assets:', this.selectedAssets.size > 0);

    // Check main containers visibility
    const patrimonioAtivosGroup = document.querySelector('.patrimonio-ativos-group');
    const ativosContentFloat = document.querySelector('.ativos-content-float');

    console.warn('\n--- MAIN CONTAINERS ---');
    console.warn('patrimonio-ativos-group:', {
      exists: !!patrimonioAtivosGroup,
      display: patrimonioAtivosGroup?.style.display || 'default',
      opacity: patrimonioAtivosGroup?.style.opacity || 'default',
    });
    console.warn('ativos-content-float:', {
      exists: !!ativosContentFloat,
      display: ativosContentFloat?.style.display || 'default',
      opacity: ativosContentFloat?.style.opacity || 'default',
    });

    console.warn('\n--- PATRIMONIO ITEMS ---');
    this.resultadoPatrimonioItems.forEach((item, key) => {
      const hasValue = this.hasValue(key);
      const isSelected = this.isAssetSelectedForResult(key);
      const shouldShow = hasValue || isSelected;
      const isVisible = item.element.style.display !== 'none';

      console.warn(`${key}:`, {
        hasValue,
        isSelected,
        shouldShow,
        isVisible,
        category: item.category,
        product: item.product,
      });
    });

    console.warn('\n--- COMISSAO ITEMS ---');
    this.resultadoComissaoItems.forEach((item, key) => {
      const hasValue = this.hasValue(key);
      const isSelected = this.isAssetSelectedForResult(key);
      const shouldShow = hasValue || isSelected;
      const isVisible = item.element.style.display !== 'none';

      console.warn(`${key}:`, {
        hasValue,
        isSelected,
        shouldShow,
        isVisible,
        category: item.category,
        product: item.product,
      });
    });

    console.warn('\n--- ATTRIBUTE MAPPING ---');
    Object.entries(this.attributeMapping).forEach(([original, mapped]) => {
      console.warn(`${original} -> ${mapped}`);
    });
  }
}

// Create singleton instance
const resultadoSync = new ResultadoSyncSystem();

// Export both class and instance
export { resultadoSync };
export default ResultadoSyncSystem;

// Expose debug methods globally for testing
if (typeof window !== 'undefined') {
  window.debugResultadoSync = () => {
    const system = window.AppCalcReino?.getModule?.('resultadoSync');
    if (system) {
      return system.debugInfo();
    }
    return null;
  };

  window.testResultadoSync = () => {
    const system = window.AppCalcReino?.getModule?.('resultadoSync');
    if (system) {
      system.testSync();
    }
  };

  window.forceResultadoSync = () => {
    const system = window.AppCalcReino?.getModule?.('resultadoSync');
    if (system) {
      system.forceSync();
    }
  };

  window.debugResultadoKeyMappings = () => {
    const system = window.AppCalcReino?.getModule?.('resultadoSync');
    if (system) {
      system.debugKeyMappings();
    }
  };
}
