/**
 * Ativo Color Tag Animation System
 * Anima as tags coloridas no container-float com base na seleção de ativos
 * Usa Motion One para animações stagger de entrada e saída
 * Controla visibilidade baseada nos steps 2 e 3 do formulário
 */
import { animate, stagger } from 'motion';

export class AtivoColorTagAnimationSystem {
  constructor() {
    this.isInitialized = false;
    this.colorTags = [];
    this.containerFloat = null;
    this.ativosItensColor = null;
    this.currentStep = 0;
    this.isInValidSteps = false; // steps 2 e 3
    this.selectedAssets = new Set();
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.initializeSystem();
    });

    this.isInitialized = true;
  }

  initializeSystem() {
    this.containerFloat = document.querySelector('.container-float');
    this.ativosItensColor = document.querySelector('.ativos-itens-color');

    if (!this.containerFloat || !this.ativosItensColor) {
      return;
    }

    this.setupColorTags();
    this.setupAssetSelectionListener();
    this.setupStepNavigationListener();
    this.setupViewportDetection();

    // Inicialmente esconder todas as tags e o container
    this.hideAllTags();
    this.updateContainerVisibility();

    // Garantir que a classe section-2 esteja correta desde o início
    this.initializeSectionClass();

    if (this.isInitialized) {
      // Expor método de debug no console (apenas em desenvolvimento)
      if (typeof window !== 'undefined') {
        window.debugAtivoColorTag = this;
      }
    }
  }

  setupColorTags() {
    const tags = this.containerFloat.querySelectorAll('.ativo-item-color-tag');

    tags.forEach((tag) => {
      const category = tag.getAttribute('ativo-category');
      const product = tag.getAttribute('ativo-product');

      if (category && product) {
        this.colorTags.push({
          element: tag,
          category: category,
          product: product,
          normalizedKey: `${this.normalizeString(category)}|${this.normalizeString(product)}`,
          isVisible: false,
        });
      }
    });
  }

  normalizeString(str) {
    return str.toLowerCase().trim();
  }

  setupStepNavigationListener() {
    // Escutar mudanças de step do sistema de navegação
    document.addEventListener('progressBarStateChange', (event) => {
      this.handleStepChange(event.detail);
    });
  }

  initializeSectionClass() {
    // Verificar qual seção está ativa inicialmente
    const activeSection = document.querySelector(
      '[data-step].active-step, [data-step][style*="display: block"]'
    );

    if (activeSection) {
      const stepAttr = activeSection.getAttribute('data-step');
      const initialStep = parseInt(stepAttr); // sistema 1-indexed
      this.updateSectionClass(initialStep);
    } else {
      // Por padrão, começar na seção 1 (step 1)
      this.updateSectionClass(1);
    }
  }

  setupViewportDetection() {
    // Detectar quando as seções 2 e 3 estão no viewport
    const section2 = document.querySelector('[data-step="2"]');
    const section3 = document.querySelector('[data-step="3"]');

    if (section2 || section3) {
      // Usar Intersection Observer para detectar viewport
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const step = entry.target.getAttribute('data-step');
              if (step === '2' || step === '3') {
                this.handleSectionInView();
              }
            }
          });
        },
        {
          threshold: 0.3, // 30% da seção visível
          rootMargin: '-50px 0px',
        }
      );

      if (section2) observer.observe(section2);
      if (section3) observer.observe(section3);
    }
  }

  handleStepChange(detail) {
    const { currentStep } = detail;
    this.currentStep = currentStep;

    // Steps válidos: 2 (seção de ativos) e 3 (seção de alocação) - sistema 1-indexed
    const wasInValidSteps = this.isInValidSteps;
    this.isInValidSteps = currentStep === 2 || currentStep === 3;

    // Atualizar classe section-2 no container
    this.updateSectionClass(currentStep);

    // Se mudou o estado de visibilidade, atualizar
    if (wasInValidSteps !== this.isInValidSteps) {
      this.updateContainerVisibility();
      this.updateTagsVisibility();
    } else if (this.isInValidSteps) {
      // Mesmo que não tenha mudado o estado, se estamos em step válido,
      // reaplique a seleção para garantir consistência
      this.applyAssetSelectionToTags();
    }
  }

  handleSectionInView() {
    // Adicionar lógica adicional baseada na detecção de viewport se necessário
    // Por enquanto, o controle principal é pelo step navigation
  }

  updateSectionClass(currentStep) {
    if (!this.ativosItensColor) return;

    // Adicionar classe section-2 APENAS quando estiver no step 2 (seção de ativos)
    // Remover classe section-2 quando estiver no step 3 (seção de alocação) ou qualquer outro step
    if (currentStep === 2) {
      this.ativosItensColor.classList.add('section-2');
    } else {
      this.ativosItensColor.classList.remove('section-2');
    }
  }

  updateContainerVisibility() {
    if (!this.containerFloat) return;

    if (this.isInValidSteps) {
      // Mostrar container quando nos steps válidos (2 e 3)
      this.containerFloat.style.display = 'block';
      this.containerFloat.style.opacity = '1';
    } else {
      // Esconder container quando fora dos steps válidos
      this.containerFloat.style.display = 'none';
      this.containerFloat.style.opacity = '0';
    }
  }

  updateTagsVisibility() {
    if (!this.isInValidSteps) {
      // Se não estamos nos steps válidos, esconder todas as tags
      this.hideAllTagsImmediate();
      return;
    }

    // Se estamos nos steps válidos, mostrar apenas as tags selecionadas
    this.applyAssetSelectionToTags();
  }

  setupAssetSelectionListener() {
    // Escutar mudanças na seleção de ativos
    document.addEventListener('assetSelectionChanged', (event) => {
      this.handleAssetSelectionChange(event.detail);
    });

    // Escutar mudanças no filtro de ativos
    document.addEventListener('assetFilterChanged', (event) => {
      this.handleAssetSelectionChange(event.detail);
    });
  }

  handleAssetSelectionChange(detail) {
    const selectedAssets = detail.selectedAssets || [];
    this.selectedAssets = new Set(selectedAssets);

    // Só aplicar mudanças se estivermos nos steps válidos
    if (this.isInValidSteps) {
      this.applyAssetSelectionToTags();
    }
  }

  applyAssetSelectionToTags() {
    // Identificar quais tags devem ser mostradas/escondidas
    const tagsToShow = [];
    const tagsToHide = [];

    this.colorTags.forEach((tag) => {
      const shouldBeVisible = this.selectedAssets.has(tag.normalizedKey);

      if (shouldBeVisible && !tag.isVisible) {
        tagsToShow.push(tag);
        tag.isVisible = true;
      } else if (!shouldBeVisible && tag.isVisible) {
        tagsToHide.push(tag);
        tag.isVisible = false;
      }
    });

    // Animar as mudanças
    if (tagsToShow.length > 0) {
      this.animateTagsIn(tagsToShow);
    }

    if (tagsToHide.length > 0) {
      this.animateTagsOut(tagsToHide);
    }
  }

  hideAllTags() {
    this.colorTags.forEach((tag) => {
      tag.element.style.opacity = '0';
      tag.element.style.transform = 'translateY(20px)';
      tag.element.style.display = 'none';
      tag.isVisible = false;
    });
  }

  hideAllTagsImmediate() {
    // Versão sem animação para mudanças de step
    this.colorTags.forEach((tag) => {
      tag.element.style.opacity = '0';
      tag.element.style.transform = 'translateY(20px)';
      tag.element.style.display = 'none';
      tag.isVisible = false; // Importante: resetar o estado
    });
  }

  animateTagsIn(tags) {
    // Primeiro mostrar os elementos
    tags.forEach((tag) => {
      tag.element.style.display = 'flex';
    });

    // Animar entrada com stagger
    const elements = tags.map((tag) => tag.element);

    // Reset initial state
    elements.forEach((element) => {
      element.style.opacity = '0';
      element.style.transform = 'translateY(20px)';
    });

    // Animate in with stagger
    animate(
      elements,
      {
        opacity: [0, 1],
        transform: ['translateY(20px)', 'translateY(0px)'],
      },
      {
        duration: 0.4,
        easing: 'ease-out',
        delay: stagger(0.1),
      }
    );
  }

  animateTagsOut(tags) {
    const elements = tags.map((tag) => tag.element);

    // Animate out with stagger (reverse order for visual appeal)
    animate(
      elements.reverse(),
      {
        opacity: [1, 0],
        transform: ['translateY(0px)', 'translateY(-10px)'],
      },
      {
        duration: 0.3,
        easing: 'ease-in',
        delay: stagger(0.05),
      }
    ).then(() => {
      // Hide elements after animation completes
      tags.forEach((tag) => {
        tag.element.style.display = 'none';
      });
    });
  }

  // Método para forçar sincronização - útil para debug
  forceSyncState() {
    if (this.isInValidSteps) {
      this.applyAssetSelectionToTags();
    }
  }

  // Métodos públicos para integração
  showTag(category, product) {
    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;
    const tag = this.colorTags.find((t) => t.normalizedKey === normalizedKey);

    if (tag && !tag.isVisible) {
      this.animateTagsIn([tag]);
    }
  }

  hideTag(category, product) {
    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;
    const tag = this.colorTags.find((t) => t.normalizedKey === normalizedKey);

    if (tag && tag.isVisible) {
      this.animateTagsOut([tag]);
    }
  }

  getAllVisibleTags() {
    return this.colorTags.filter((tag) => tag.isVisible);
  }

  getTagByAsset(category, product) {
    const normalizedKey = `${this.normalizeString(category)}|${this.normalizeString(product)}`;
    return this.colorTags.find((t) => t.normalizedKey === normalizedKey);
  }
}
